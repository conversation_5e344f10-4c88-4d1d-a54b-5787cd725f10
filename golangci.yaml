version: "2"
linters:
  default: none
  enable:
    - bodyclose
    - dogsled
    - gocritic
    - govet
    - ineffassign
    - misspell
    - nakedret
    - revive
    - rowserrcheck
    - staticcheck
    - unconvert
    - unused
    - whitespace
  settings:
    revive:
      rules:
        - name: error-strings
          disabled: true
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    paths:
      - third_party$
      - builtin$
      - examples$
formatters:
  enable:
    - goimports
  settings:
    goimports:
      local-prefixes:
        - github.com/buildpacks/pack
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
issues:
  default: info
  rules:
    - linters:
      - staticcheck: info