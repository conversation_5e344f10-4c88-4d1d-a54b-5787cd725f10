package fakes

import (
	"io"
	"os"
	"testing"

	"github.com/buildpacks/pack/pkg/dist"
	h "github.com/buildpacks/pack/testhelpers"
)

func CreateBuildpackTar(t *testing.T, tmpDir string, descriptor dist.BuildpackDescriptor) string {
	buildpack, err := NewFakeBuildpackBlob(&descriptor, 0777)
	h.Assert<PERSON>il(t, err)

	tempFile, err := os.CreateTemp(tmpDir, "bp-*.tar")
	h.Assert<PERSON>il(t, err)
	defer tempFile.Close()

	reader, err := buildpack.Open()
	h.AssertNil(t, err)

	_, err = io.Copy(tempFile, reader)
	h.<PERSON>sert<PERSON>il(t, err)

	return tempFile.Name()
}
