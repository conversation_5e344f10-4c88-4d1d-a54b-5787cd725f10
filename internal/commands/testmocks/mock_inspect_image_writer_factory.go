// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/buildpacks/pack/internal/commands (interfaces: InspectImageWriterFactory)

// Package testmocks is a generated GoMock package.
package testmocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	writer "github.com/buildpacks/pack/internal/inspectimage/writer"
)

// MockInspectImageWriterFactory is a mock of InspectImageWriterFactory interface.
type MockInspectImageWriterFactory struct {
	ctrl     *gomock.Controller
	recorder *MockInspectImageWriterFactoryMockRecorder
}

// MockInspectImageWriterFactoryMockRecorder is the mock recorder for MockInspectImageWriterFactory.
type MockInspectImageWriterFactoryMockRecorder struct {
	mock *MockInspectImageWriterFactory
}

// NewMockInspectImageWriterFactory creates a new mock instance.
func NewMockInspectImageWriterFactory(ctrl *gomock.Controller) *MockInspectImageWriterFactory {
	mock := &MockInspectImageWriterFactory{ctrl: ctrl}
	mock.recorder = &MockInspectImageWriterFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInspectImageWriterFactory) EXPECT() *MockInspectImageWriterFactoryMockRecorder {
	return m.recorder
}

// Writer mocks base method.
func (m *MockInspectImageWriterFactory) Writer(arg0 string, arg1 bool) (writer.InspectImageWriter, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Writer", arg0, arg1)
	ret0, _ := ret[0].(writer.InspectImageWriter)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Writer indicates an expected call of Writer.
func (mr *MockInspectImageWriterFactoryMockRecorder) Writer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Writer", reflect.TypeOf((*MockInspectImageWriterFactory)(nil).Writer), arg0, arg1)
}
