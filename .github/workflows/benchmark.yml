name: benchmark
on:
  push:
    branches:
      - main

permissions:
  contents: write
  deployments: write

jobs:
  benchmark:
    name: Run Go benchmark example
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up go
        uses: actions/setup-go@v5
        with:
          go-version: 1.24
          check-latest: true
          go-version-file: 'go.mod'
      - name: Set up go env
        run: |
          echo "GOPATH=$(go env GOPATH)" >> $GITHUB_ENV
          echo "$(go env GOPATH)/bin" >> $GITHUB_PATH
        shell: bash
      - name: Run benchmark
        run: |
             git clone https://github.com/buildpacks/samples.git
             mkdir out || (exit 0)
             go test -bench=. -benchtime=1s ./benchmarks/... -tags=benchmarks | tee ./out/benchmark.txt

      - name: Store benchmark result
        uses: benchmark-action/github-action-benchmark@v1
        with:
          name: Go Benchmark
          tool: 'go'
          output-file-path: ./out/benchmark.txt
          github-token: ${{ secrets.GITHUB_TOKEN }}
          auto-push: true
          # Show alert with commit comment on detecting possible performance regression
          alert-threshold: '200%'
          comment-on-alert: true
          fail-on-alert: true
          alert-comment-cc-users: '@buildpacks/platform-maintainers'
