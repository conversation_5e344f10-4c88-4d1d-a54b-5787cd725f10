name: delivery / chocolatey

on:
  release:
    types:
      - released
  workflow_dispatch:
    inputs:
      tag_name:
        description: The release tag to distribute
        required: true

env:
  CHOCO_PATH: chocolatey

defaults:
  run:
    shell: bash

jobs:
  deliver-chocolatey:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
      - name: Determine version
        uses: actions/github-script@v7
        id: version
        with:
          result-encoding: string
          script: |
            let tag = (context.payload.release && context.payload.release.tag_name)
              || (context.payload.inputs && context.payload.inputs.tag_name);

            if (!tag) {
              throw "ERROR: unable to determine tag";
            }

            updatedTag = tag.replace(/^v/, '')
            core.exportVariable('PACK_VERSION', updatedTag);
            return updatedTag;
      - name: Setup working dir
        run: |
          mkdir -p ${{ env.CHOCO_PATH }}/source
          cp -r .github/workflows/delivery/chocolatey/. ${{ env.CHOCO_PATH }}/
          ls -R ${{ env.CHOCO_PATH }}
      - name: Download and unzip Pack (Windows)
        run: |
          url="https://github.com/buildpacks/pack/releases/download/v${{ env.PACK_VERSION }}/pack-v${{ env.PACK_VERSION }}-windows.zip"
          filename=pack.zip
          tools_path=${{ env.CHOCO_PATH }}/tools/
          zip_path="$tools_path/$filename"

          curl -sSL "$url" -o "$zip_path"

          apt-get update && apt-get install unzip
          unzip -o "$zip_path" -d $tools_path
          rm "$zip_path"
          ls $tools_path
      - name: Fill nuspec
        run: |
          file=${{ env.CHOCO_PATH }}/pack.nuspec
          sed -i "s/{{PACK_VERSION}}/${{ env.PACK_VERSION }}/g" $file
          cat $file
      - name: Fill License
        run: |
          file="${{ env.CHOCO_PATH }}/tools/LICENSE.txt"
          cp LICENSE $file
          cat $file
      - name: build-release
        uses: crazy-max/ghaction-chocolatey@v3
        with:
          args: pack ${{ env.CHOCO_PATH }}/pack.nuspec --outputdirectory ${{ env.CHOCO_PATH}}/source
      - name: list files
        run: |
          ls ${{ env.CHOCO_PATH }}
          ls ${{ env.CHOCO_PATH }}/tools
      - name: Test Release
        uses: crazy-max/ghaction-chocolatey@v3
        with:
          args: install pack -s ${{ env.CHOCO_PATH }}/source
      - name: Ensure Pack Installed
        run: pack help
      - name: Upload Release
        uses: crazy-max/ghaction-chocolatey@v3
        with:
          args: push ${{ env.CHOCO_PATH }}/source/pack.${{ env.PACK_VERSION }}.nupkg -s https://push.chocolatey.org/ -k ${{ secrets.CHOCO_KEY }}
