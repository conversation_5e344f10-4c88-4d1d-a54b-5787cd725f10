image_name = "{{.image_name}}"

[remote_info]
stack = "pack.test.stack"
rebasable = {{.rebasable}}

  [remote_info.base_image]
  top_layer = "{{.base_image_top_layer}}"
  reference = "{{.base_image_ref}}"

  [[remote_info.run_images]]
  name = "pack-test/run"

  [[remote_info.run_images]]
  name = "{{.run_image_mirror}}"

  [[remote_info.buildpacks]]
  id = "simple/layers"
  version = "simple-layers-version"

  [[remote_info.processes]]
  type = "web"
  shell = "bash"
  command = "{{( StringsEscapeBackslash .web_command )}}"
  default = true
  args = [ "8080" ]
  working-dir = "{{ ( StringsEscapeBackslash .image_workdir ) }}"

  [[remote_info.processes]]
  type = "hello"
  shell = ""
  command = "{{.hello_command}}"
  default = false
  args = [ {{ ( StringsJoin (StringsDoubleQuote .hello_args) "," ) }} ]
  working-dir = "{{ ( StringsEscapeBackslash .image_workdir ) }}"

