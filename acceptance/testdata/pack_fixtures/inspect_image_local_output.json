{
  "image_name": "{{.image_name}}",
  "remote_info": null,
  "local_info": {
    "stack": "pack.test.stack",
    "rebasable": {{.rebasable}},
    "base_image": {
      "top_layer": "{{.base_image_top_layer}}",
      "reference": "{{.base_image_id}}"
    },
    "run_images": [
      {
        "name": "{{.run_image_local_mirror}}",
        "user_configured": true
      },
      {
        "name": "pack-test/run"
      },
      {
        "name": "{{.run_image_mirror}}"
      }
    ],
    "buildpacks": [
      {
        "id": "simple/layers",
        "version": "simple-layers-version"
      }
    ],
    "extensions": null,
    "processes": [
      {
        "type": "web",
        "shell": "bash",
        "command": "{{ ( StringsEscapeBackslash .web_command ) }}",
        "default": true,
        "args": [
          "8080"
        ],
        "working-dir": "{{ ( StringsEscapeBackslash .image_workdir ) }}"
      },
      {
        "type": "hello",
        "shell": "",
        "command": "{{.hello_command}}",
        "default": false,
        "args": [
          {{ ( <PERSON><PERSON>oin (StringsDoubleQuote .hello_args) "," ) }}
        ],
        "working-dir": "{{ ( StringsEscapeBackslash .image_workdir ) }}"
      }
    ]
  }
}
