#!/usr/bin/env bash

echo "---> Build: Simple Layers Different Sha Buildpack"

set -o errexit
set -o nounset
set -o pipefail

launch_dir=$1

## makes a launch layer
echo "making launch layer"

# Add color line, to test for --no-color
echo "Color: [0mStyled"

mkdir "$launch_dir/launch-layer"
echo "Launch Dep Contents" > "$launch_dir/launch-layer/launch-dep"
ln -snf "$launch_dir/launch-layer" launch-deps
echo "[types]" > "$launch_dir/launch-layer.toml"
echo "launch = true" >> "$launch_dir/launch-layer.toml"

## makes a cached launch layer
if [[ ! -f "$launch_dir/cached-launch-layer.toml" ]]; then
    echo "making cached launch layer"
    mkdir "$launch_dir/cached-launch-layer"
    echo "Cached Dep Contents" > "$launch_dir/cached-launch-layer/cached-dep"
    ln -snf "$launch_dir/cached-launch-layer" cached-deps
    echo "[types]" > "$launch_dir/cached-launch-layer.toml"
    echo "launch = true" >> "$launch_dir/cached-launch-layer.toml"
    echo "cache = true" >> "$launch_dir/cached-launch-layer.toml"
else
  echo "reusing cached launch layer"
  ln -snf "$launch_dir/cached-launch-layer" cached-deps
fi

## adds a process
cat <<EOF > "$launch_dir/launch.toml"
[[processes]]
  type = "web"
  command = "./run"
  args = ["8080"]
  default = true

[[processes]]
  type = "hello"
  command = "echo"
  args = ["hello", "world"]
  direct = true
EOF

echo "---> Done"
