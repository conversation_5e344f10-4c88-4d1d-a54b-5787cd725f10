// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/buildpacks/pack/pkg/client (interfaces: IndexFactory)

// Package testmocks is a generated GoMock package.
package testmocks

import (
	reflect "reflect"

	imgutil "github.com/buildpacks/imgutil"
	gomock "github.com/golang/mock/gomock"
)

// MockIndexFactory is a mock of IndexFactory interface.
type MockIndexFactory struct {
	ctrl     *gomock.Controller
	recorder *MockIndexFactoryMockRecorder
}

// MockIndexFactoryMockRecorder is the mock recorder for MockIndexFactory.
type MockIndexFactoryMockRecorder struct {
	mock *MockIndexFactory
}

// NewMockIndexFactory creates a new mock instance.
func NewMockIndexFactory(ctrl *gomock.Controller) *MockIndexFactory {
	mock := &MockIndexFactory{ctrl: ctrl}
	mock.recorder = &MockIndexFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIndexFactory) EXPECT() *MockIndexFactoryMockRecorder {
	return m.recorder
}

// CreateIndex mocks base method.
func (m *MockIndexFactory) CreateIndex(arg0 string, arg1 ...imgutil.IndexOption) (imgutil.ImageIndex, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateIndex", varargs...)
	ret0, _ := ret[0].(imgutil.ImageIndex)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIndex indicates an expected call of CreateIndex.
func (mr *MockIndexFactoryMockRecorder) CreateIndex(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIndex", reflect.TypeOf((*MockIndexFactory)(nil).CreateIndex), varargs...)
}

// Exists mocks base method.
func (m *MockIndexFactory) Exists(arg0 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Exists", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Exists indicates an expected call of Exists.
func (mr *MockIndexFactoryMockRecorder) Exists(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exists", reflect.TypeOf((*MockIndexFactory)(nil).Exists), arg0)
}

// FetchIndex mocks base method.
func (m *MockIndexFactory) FetchIndex(arg0 string, arg1 ...imgutil.IndexOption) (imgutil.ImageIndex, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchIndex", varargs...)
	ret0, _ := ret[0].(imgutil.ImageIndex)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchIndex indicates an expected call of FetchIndex.
func (mr *MockIndexFactoryMockRecorder) FetchIndex(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchIndex", reflect.TypeOf((*MockIndexFactory)(nil).FetchIndex), varargs...)
}

// FindIndex mocks base method.
func (m *MockIndexFactory) FindIndex(arg0 string, arg1 ...imgutil.IndexOption) (imgutil.ImageIndex, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindIndex", varargs...)
	ret0, _ := ret[0].(imgutil.ImageIndex)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindIndex indicates an expected call of FindIndex.
func (mr *MockIndexFactoryMockRecorder) FindIndex(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindIndex", reflect.TypeOf((*MockIndexFactory)(nil).FindIndex), varargs...)
}

// LoadIndex mocks base method.
func (m *MockIndexFactory) LoadIndex(arg0 string, arg1 ...imgutil.IndexOption) (imgutil.ImageIndex, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadIndex", varargs...)
	ret0, _ := ret[0].(imgutil.ImageIndex)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadIndex indicates an expected call of LoadIndex.
func (mr *MockIndexFactoryMockRecorder) LoadIndex(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadIndex", reflect.TypeOf((*MockIndexFactory)(nil).LoadIndex), varargs...)
}
