// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/docker/docker/client (interfaces: CommonAPIClient)

// Package testmocks is a generated GoMock package.
package testmocks

import (
	context "context"
	io "io"
	net "net"
	http "net/http"
	reflect "reflect"

	types "github.com/docker/docker/api/types"
	common "github.com/docker/docker/api/types/common"
	container "github.com/docker/docker/api/types/container"
	events "github.com/docker/docker/api/types/events"
	filters "github.com/docker/docker/api/types/filters"
	image "github.com/docker/docker/api/types/image"
	network "github.com/docker/docker/api/types/network"
	registry "github.com/docker/docker/api/types/registry"
	swarm "github.com/docker/docker/api/types/swarm"
	system "github.com/docker/docker/api/types/system"
	volume "github.com/docker/docker/api/types/volume"
	client "github.com/docker/docker/client"
	gomock "github.com/golang/mock/gomock"
	v1 "github.com/opencontainers/image-spec/specs-go/v1"
)

// MockCommonAPIClient is a mock of CommonAPIClient interface.
type MockCommonAPIClient struct {
	ctrl     *gomock.Controller
	recorder *MockCommonAPIClientMockRecorder
}

// MockCommonAPIClientMockRecorder is the mock recorder for MockCommonAPIClient.
type MockCommonAPIClientMockRecorder struct {
	mock *MockCommonAPIClient
}

// NewMockCommonAPIClient creates a new mock instance.
func NewMockCommonAPIClient(ctrl *gomock.Controller) *MockCommonAPIClient {
	mock := &MockCommonAPIClient{ctrl: ctrl}
	mock.recorder = &MockCommonAPIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCommonAPIClient) EXPECT() *MockCommonAPIClientMockRecorder {
	return m.recorder
}

// BuildCachePrune mocks base method.
func (m *MockCommonAPIClient) BuildCachePrune(arg0 context.Context, arg1 types.BuildCachePruneOptions) (*types.BuildCachePruneReport, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BuildCachePrune", arg0, arg1)
	ret0, _ := ret[0].(*types.BuildCachePruneReport)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuildCachePrune indicates an expected call of BuildCachePrune.
func (mr *MockCommonAPIClientMockRecorder) BuildCachePrune(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildCachePrune", reflect.TypeOf((*MockCommonAPIClient)(nil).BuildCachePrune), arg0, arg1)
}

// BuildCancel mocks base method.
func (m *MockCommonAPIClient) BuildCancel(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BuildCancel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BuildCancel indicates an expected call of BuildCancel.
func (mr *MockCommonAPIClientMockRecorder) BuildCancel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildCancel", reflect.TypeOf((*MockCommonAPIClient)(nil).BuildCancel), arg0, arg1)
}

// ClientVersion mocks base method.
func (m *MockCommonAPIClient) ClientVersion() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientVersion")
	ret0, _ := ret[0].(string)
	return ret0
}

// ClientVersion indicates an expected call of ClientVersion.
func (mr *MockCommonAPIClientMockRecorder) ClientVersion() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientVersion", reflect.TypeOf((*MockCommonAPIClient)(nil).ClientVersion))
}

// Close mocks base method.
func (m *MockCommonAPIClient) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockCommonAPIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockCommonAPIClient)(nil).Close))
}

// ConfigCreate mocks base method.
func (m *MockCommonAPIClient) ConfigCreate(arg0 context.Context, arg1 swarm.ConfigSpec) (types.ConfigCreateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfigCreate", arg0, arg1)
	ret0, _ := ret[0].(types.ConfigCreateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfigCreate indicates an expected call of ConfigCreate.
func (mr *MockCommonAPIClientMockRecorder) ConfigCreate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigCreate", reflect.TypeOf((*MockCommonAPIClient)(nil).ConfigCreate), arg0, arg1)
}

// ConfigInspectWithRaw mocks base method.
func (m *MockCommonAPIClient) ConfigInspectWithRaw(arg0 context.Context, arg1 string) (swarm.Config, []byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfigInspectWithRaw", arg0, arg1)
	ret0, _ := ret[0].(swarm.Config)
	ret1, _ := ret[1].([]byte)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ConfigInspectWithRaw indicates an expected call of ConfigInspectWithRaw.
func (mr *MockCommonAPIClientMockRecorder) ConfigInspectWithRaw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigInspectWithRaw", reflect.TypeOf((*MockCommonAPIClient)(nil).ConfigInspectWithRaw), arg0, arg1)
}

// ConfigList mocks base method.
func (m *MockCommonAPIClient) ConfigList(arg0 context.Context, arg1 types.ConfigListOptions) ([]swarm.Config, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfigList", arg0, arg1)
	ret0, _ := ret[0].([]swarm.Config)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfigList indicates an expected call of ConfigList.
func (mr *MockCommonAPIClientMockRecorder) ConfigList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigList", reflect.TypeOf((*MockCommonAPIClient)(nil).ConfigList), arg0, arg1)
}

// ConfigRemove mocks base method.
func (m *MockCommonAPIClient) ConfigRemove(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfigRemove", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ConfigRemove indicates an expected call of ConfigRemove.
func (mr *MockCommonAPIClientMockRecorder) ConfigRemove(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigRemove", reflect.TypeOf((*MockCommonAPIClient)(nil).ConfigRemove), arg0, arg1)
}

// ConfigUpdate mocks base method.
func (m *MockCommonAPIClient) ConfigUpdate(arg0 context.Context, arg1 string, arg2 swarm.Version, arg3 swarm.ConfigSpec) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfigUpdate", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// ConfigUpdate indicates an expected call of ConfigUpdate.
func (mr *MockCommonAPIClientMockRecorder) ConfigUpdate(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigUpdate", reflect.TypeOf((*MockCommonAPIClient)(nil).ConfigUpdate), arg0, arg1, arg2, arg3)
}

// ContainerAttach mocks base method.
func (m *MockCommonAPIClient) ContainerAttach(arg0 context.Context, arg1 string, arg2 container.AttachOptions) (types.HijackedResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerAttach", arg0, arg1, arg2)
	ret0, _ := ret[0].(types.HijackedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerAttach indicates an expected call of ContainerAttach.
func (mr *MockCommonAPIClientMockRecorder) ContainerAttach(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerAttach", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerAttach), arg0, arg1, arg2)
}

// ContainerCommit mocks base method.
func (m *MockCommonAPIClient) ContainerCommit(arg0 context.Context, arg1 string, arg2 container.CommitOptions) (common.IDResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerCommit", arg0, arg1, arg2)
	ret0, _ := ret[0].(common.IDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerCommit indicates an expected call of ContainerCommit.
func (mr *MockCommonAPIClientMockRecorder) ContainerCommit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerCommit", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerCommit), arg0, arg1, arg2)
}

// ContainerCreate mocks base method.
func (m *MockCommonAPIClient) ContainerCreate(arg0 context.Context, arg1 *container.Config, arg2 *container.HostConfig, arg3 *network.NetworkingConfig, arg4 *v1.Platform, arg5 string) (container.CreateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerCreate", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(container.CreateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerCreate indicates an expected call of ContainerCreate.
func (mr *MockCommonAPIClientMockRecorder) ContainerCreate(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerCreate", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerCreate), arg0, arg1, arg2, arg3, arg4, arg5)
}

// ContainerDiff mocks base method.
func (m *MockCommonAPIClient) ContainerDiff(arg0 context.Context, arg1 string) ([]container.FilesystemChange, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerDiff", arg0, arg1)
	ret0, _ := ret[0].([]container.FilesystemChange)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerDiff indicates an expected call of ContainerDiff.
func (mr *MockCommonAPIClientMockRecorder) ContainerDiff(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerDiff", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerDiff), arg0, arg1)
}

// ContainerExecAttach mocks base method.
func (m *MockCommonAPIClient) ContainerExecAttach(arg0 context.Context, arg1 string, arg2 container.ExecStartOptions) (types.HijackedResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerExecAttach", arg0, arg1, arg2)
	ret0, _ := ret[0].(types.HijackedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerExecAttach indicates an expected call of ContainerExecAttach.
func (mr *MockCommonAPIClientMockRecorder) ContainerExecAttach(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerExecAttach", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerExecAttach), arg0, arg1, arg2)
}

// ContainerExecCreate mocks base method.
func (m *MockCommonAPIClient) ContainerExecCreate(arg0 context.Context, arg1 string, arg2 container.ExecOptions) (common.IDResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerExecCreate", arg0, arg1, arg2)
	ret0, _ := ret[0].(common.IDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerExecCreate indicates an expected call of ContainerExecCreate.
func (mr *MockCommonAPIClientMockRecorder) ContainerExecCreate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerExecCreate", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerExecCreate), arg0, arg1, arg2)
}

// ContainerExecInspect mocks base method.
func (m *MockCommonAPIClient) ContainerExecInspect(arg0 context.Context, arg1 string) (container.ExecInspect, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerExecInspect", arg0, arg1)
	ret0, _ := ret[0].(container.ExecInspect)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerExecInspect indicates an expected call of ContainerExecInspect.
func (mr *MockCommonAPIClientMockRecorder) ContainerExecInspect(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerExecInspect", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerExecInspect), arg0, arg1)
}

// ContainerExecResize mocks base method.
func (m *MockCommonAPIClient) ContainerExecResize(arg0 context.Context, arg1 string, arg2 container.ResizeOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerExecResize", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ContainerExecResize indicates an expected call of ContainerExecResize.
func (mr *MockCommonAPIClientMockRecorder) ContainerExecResize(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerExecResize", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerExecResize), arg0, arg1, arg2)
}

// ContainerExecStart mocks base method.
func (m *MockCommonAPIClient) ContainerExecStart(arg0 context.Context, arg1 string, arg2 container.ExecStartOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerExecStart", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ContainerExecStart indicates an expected call of ContainerExecStart.
func (mr *MockCommonAPIClientMockRecorder) ContainerExecStart(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerExecStart", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerExecStart), arg0, arg1, arg2)
}

// ContainerExport mocks base method.
func (m *MockCommonAPIClient) ContainerExport(arg0 context.Context, arg1 string) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerExport", arg0, arg1)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerExport indicates an expected call of ContainerExport.
func (mr *MockCommonAPIClientMockRecorder) ContainerExport(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerExport", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerExport), arg0, arg1)
}

// ContainerInspect mocks base method.
func (m *MockCommonAPIClient) ContainerInspect(arg0 context.Context, arg1 string) (container.InspectResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerInspect", arg0, arg1)
	ret0, _ := ret[0].(container.InspectResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerInspect indicates an expected call of ContainerInspect.
func (mr *MockCommonAPIClientMockRecorder) ContainerInspect(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerInspect", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerInspect), arg0, arg1)
}

// ContainerInspectWithRaw mocks base method.
func (m *MockCommonAPIClient) ContainerInspectWithRaw(arg0 context.Context, arg1 string, arg2 bool) (container.InspectResponse, []byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerInspectWithRaw", arg0, arg1, arg2)
	ret0, _ := ret[0].(container.InspectResponse)
	ret1, _ := ret[1].([]byte)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ContainerInspectWithRaw indicates an expected call of ContainerInspectWithRaw.
func (mr *MockCommonAPIClientMockRecorder) ContainerInspectWithRaw(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerInspectWithRaw", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerInspectWithRaw), arg0, arg1, arg2)
}

// ContainerKill mocks base method.
func (m *MockCommonAPIClient) ContainerKill(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerKill", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ContainerKill indicates an expected call of ContainerKill.
func (mr *MockCommonAPIClientMockRecorder) ContainerKill(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerKill", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerKill), arg0, arg1, arg2)
}

// ContainerList mocks base method.
func (m *MockCommonAPIClient) ContainerList(arg0 context.Context, arg1 container.ListOptions) ([]container.Summary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerList", arg0, arg1)
	ret0, _ := ret[0].([]container.Summary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerList indicates an expected call of ContainerList.
func (mr *MockCommonAPIClientMockRecorder) ContainerList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerList", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerList), arg0, arg1)
}

// ContainerLogs mocks base method.
func (m *MockCommonAPIClient) ContainerLogs(arg0 context.Context, arg1 string, arg2 container.LogsOptions) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerLogs", arg0, arg1, arg2)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerLogs indicates an expected call of ContainerLogs.
func (mr *MockCommonAPIClientMockRecorder) ContainerLogs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerLogs", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerLogs), arg0, arg1, arg2)
}

// ContainerPause mocks base method.
func (m *MockCommonAPIClient) ContainerPause(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerPause", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ContainerPause indicates an expected call of ContainerPause.
func (mr *MockCommonAPIClientMockRecorder) ContainerPause(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerPause", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerPause), arg0, arg1)
}

// ContainerRemove mocks base method.
func (m *MockCommonAPIClient) ContainerRemove(arg0 context.Context, arg1 string, arg2 container.RemoveOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerRemove", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ContainerRemove indicates an expected call of ContainerRemove.
func (mr *MockCommonAPIClientMockRecorder) ContainerRemove(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerRemove", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerRemove), arg0, arg1, arg2)
}

// ContainerRename mocks base method.
func (m *MockCommonAPIClient) ContainerRename(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerRename", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ContainerRename indicates an expected call of ContainerRename.
func (mr *MockCommonAPIClientMockRecorder) ContainerRename(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerRename", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerRename), arg0, arg1, arg2)
}

// ContainerResize mocks base method.
func (m *MockCommonAPIClient) ContainerResize(arg0 context.Context, arg1 string, arg2 container.ResizeOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerResize", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ContainerResize indicates an expected call of ContainerResize.
func (mr *MockCommonAPIClientMockRecorder) ContainerResize(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerResize", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerResize), arg0, arg1, arg2)
}

// ContainerRestart mocks base method.
func (m *MockCommonAPIClient) ContainerRestart(arg0 context.Context, arg1 string, arg2 container.StopOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerRestart", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ContainerRestart indicates an expected call of ContainerRestart.
func (mr *MockCommonAPIClientMockRecorder) ContainerRestart(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerRestart", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerRestart), arg0, arg1, arg2)
}

// ContainerStart mocks base method.
func (m *MockCommonAPIClient) ContainerStart(arg0 context.Context, arg1 string, arg2 container.StartOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerStart", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ContainerStart indicates an expected call of ContainerStart.
func (mr *MockCommonAPIClientMockRecorder) ContainerStart(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerStart", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerStart), arg0, arg1, arg2)
}

// ContainerStatPath mocks base method.
func (m *MockCommonAPIClient) ContainerStatPath(arg0 context.Context, arg1, arg2 string) (container.PathStat, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerStatPath", arg0, arg1, arg2)
	ret0, _ := ret[0].(container.PathStat)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerStatPath indicates an expected call of ContainerStatPath.
func (mr *MockCommonAPIClientMockRecorder) ContainerStatPath(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerStatPath", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerStatPath), arg0, arg1, arg2)
}

// ContainerStats mocks base method.
func (m *MockCommonAPIClient) ContainerStats(arg0 context.Context, arg1 string, arg2 bool) (container.StatsResponseReader, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerStats", arg0, arg1, arg2)
	ret0, _ := ret[0].(container.StatsResponseReader)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerStats indicates an expected call of ContainerStats.
func (mr *MockCommonAPIClientMockRecorder) ContainerStats(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerStats", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerStats), arg0, arg1, arg2)
}

// ContainerStatsOneShot mocks base method.
func (m *MockCommonAPIClient) ContainerStatsOneShot(arg0 context.Context, arg1 string) (container.StatsResponseReader, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerStatsOneShot", arg0, arg1)
	ret0, _ := ret[0].(container.StatsResponseReader)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerStatsOneShot indicates an expected call of ContainerStatsOneShot.
func (mr *MockCommonAPIClientMockRecorder) ContainerStatsOneShot(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerStatsOneShot", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerStatsOneShot), arg0, arg1)
}

// ContainerStop mocks base method.
func (m *MockCommonAPIClient) ContainerStop(arg0 context.Context, arg1 string, arg2 container.StopOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerStop", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ContainerStop indicates an expected call of ContainerStop.
func (mr *MockCommonAPIClientMockRecorder) ContainerStop(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerStop", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerStop), arg0, arg1, arg2)
}

// ContainerTop mocks base method.
func (m *MockCommonAPIClient) ContainerTop(arg0 context.Context, arg1 string, arg2 []string) (container.TopResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerTop", arg0, arg1, arg2)
	ret0, _ := ret[0].(container.TopResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerTop indicates an expected call of ContainerTop.
func (mr *MockCommonAPIClientMockRecorder) ContainerTop(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerTop", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerTop), arg0, arg1, arg2)
}

// ContainerUnpause mocks base method.
func (m *MockCommonAPIClient) ContainerUnpause(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerUnpause", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ContainerUnpause indicates an expected call of ContainerUnpause.
func (mr *MockCommonAPIClientMockRecorder) ContainerUnpause(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerUnpause", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerUnpause), arg0, arg1)
}

// ContainerUpdate mocks base method.
func (m *MockCommonAPIClient) ContainerUpdate(arg0 context.Context, arg1 string, arg2 container.UpdateConfig) (container.UpdateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerUpdate", arg0, arg1, arg2)
	ret0, _ := ret[0].(container.UpdateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainerUpdate indicates an expected call of ContainerUpdate.
func (mr *MockCommonAPIClientMockRecorder) ContainerUpdate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerUpdate", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerUpdate), arg0, arg1, arg2)
}

// ContainerWait mocks base method.
func (m *MockCommonAPIClient) ContainerWait(arg0 context.Context, arg1 string, arg2 container.WaitCondition) (<-chan container.WaitResponse, <-chan error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainerWait", arg0, arg1, arg2)
	ret0, _ := ret[0].(<-chan container.WaitResponse)
	ret1, _ := ret[1].(<-chan error)
	return ret0, ret1
}

// ContainerWait indicates an expected call of ContainerWait.
func (mr *MockCommonAPIClientMockRecorder) ContainerWait(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainerWait", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainerWait), arg0, arg1, arg2)
}

// ContainersPrune mocks base method.
func (m *MockCommonAPIClient) ContainersPrune(arg0 context.Context, arg1 filters.Args) (container.PruneReport, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ContainersPrune", arg0, arg1)
	ret0, _ := ret[0].(container.PruneReport)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContainersPrune indicates an expected call of ContainersPrune.
func (mr *MockCommonAPIClientMockRecorder) ContainersPrune(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContainersPrune", reflect.TypeOf((*MockCommonAPIClient)(nil).ContainersPrune), arg0, arg1)
}

// CopyFromContainer mocks base method.
func (m *MockCommonAPIClient) CopyFromContainer(arg0 context.Context, arg1, arg2 string) (io.ReadCloser, container.PathStat, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyFromContainer", arg0, arg1, arg2)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(container.PathStat)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CopyFromContainer indicates an expected call of CopyFromContainer.
func (mr *MockCommonAPIClientMockRecorder) CopyFromContainer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyFromContainer", reflect.TypeOf((*MockCommonAPIClient)(nil).CopyFromContainer), arg0, arg1, arg2)
}

// CopyToContainer mocks base method.
func (m *MockCommonAPIClient) CopyToContainer(arg0 context.Context, arg1, arg2 string, arg3 io.Reader, arg4 container.CopyToContainerOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyToContainer", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// CopyToContainer indicates an expected call of CopyToContainer.
func (mr *MockCommonAPIClientMockRecorder) CopyToContainer(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyToContainer", reflect.TypeOf((*MockCommonAPIClient)(nil).CopyToContainer), arg0, arg1, arg2, arg3, arg4)
}

// DaemonHost mocks base method.
func (m *MockCommonAPIClient) DaemonHost() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DaemonHost")
	ret0, _ := ret[0].(string)
	return ret0
}

// DaemonHost indicates an expected call of DaemonHost.
func (mr *MockCommonAPIClientMockRecorder) DaemonHost() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DaemonHost", reflect.TypeOf((*MockCommonAPIClient)(nil).DaemonHost))
}

// DialHijack mocks base method.
func (m *MockCommonAPIClient) DialHijack(arg0 context.Context, arg1, arg2 string, arg3 map[string][]string) (net.Conn, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DialHijack", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(net.Conn)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DialHijack indicates an expected call of DialHijack.
func (mr *MockCommonAPIClientMockRecorder) DialHijack(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DialHijack", reflect.TypeOf((*MockCommonAPIClient)(nil).DialHijack), arg0, arg1, arg2, arg3)
}

// Dialer mocks base method.
func (m *MockCommonAPIClient) Dialer() func(context.Context) (net.Conn, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Dialer")
	ret0, _ := ret[0].(func(context.Context) (net.Conn, error))
	return ret0
}

// Dialer indicates an expected call of Dialer.
func (mr *MockCommonAPIClientMockRecorder) Dialer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Dialer", reflect.TypeOf((*MockCommonAPIClient)(nil).Dialer))
}

// DiskUsage mocks base method.
func (m *MockCommonAPIClient) DiskUsage(arg0 context.Context, arg1 types.DiskUsageOptions) (types.DiskUsage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DiskUsage", arg0, arg1)
	ret0, _ := ret[0].(types.DiskUsage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DiskUsage indicates an expected call of DiskUsage.
func (mr *MockCommonAPIClientMockRecorder) DiskUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DiskUsage", reflect.TypeOf((*MockCommonAPIClient)(nil).DiskUsage), arg0, arg1)
}

// DistributionInspect mocks base method.
func (m *MockCommonAPIClient) DistributionInspect(arg0 context.Context, arg1, arg2 string) (registry.DistributionInspect, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DistributionInspect", arg0, arg1, arg2)
	ret0, _ := ret[0].(registry.DistributionInspect)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DistributionInspect indicates an expected call of DistributionInspect.
func (mr *MockCommonAPIClientMockRecorder) DistributionInspect(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DistributionInspect", reflect.TypeOf((*MockCommonAPIClient)(nil).DistributionInspect), arg0, arg1, arg2)
}

// Events mocks base method.
func (m *MockCommonAPIClient) Events(arg0 context.Context, arg1 events.ListOptions) (<-chan events.Message, <-chan error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Events", arg0, arg1)
	ret0, _ := ret[0].(<-chan events.Message)
	ret1, _ := ret[1].(<-chan error)
	return ret0, ret1
}

// Events indicates an expected call of Events.
func (mr *MockCommonAPIClientMockRecorder) Events(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Events", reflect.TypeOf((*MockCommonAPIClient)(nil).Events), arg0, arg1)
}

// HTTPClient mocks base method.
func (m *MockCommonAPIClient) HTTPClient() *http.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HTTPClient")
	ret0, _ := ret[0].(*http.Client)
	return ret0
}

// HTTPClient indicates an expected call of HTTPClient.
func (mr *MockCommonAPIClientMockRecorder) HTTPClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HTTPClient", reflect.TypeOf((*MockCommonAPIClient)(nil).HTTPClient))
}

// ImageBuild mocks base method.
func (m *MockCommonAPIClient) ImageBuild(arg0 context.Context, arg1 io.Reader, arg2 types.ImageBuildOptions) (types.ImageBuildResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImageBuild", arg0, arg1, arg2)
	ret0, _ := ret[0].(types.ImageBuildResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImageBuild indicates an expected call of ImageBuild.
func (mr *MockCommonAPIClientMockRecorder) ImageBuild(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImageBuild", reflect.TypeOf((*MockCommonAPIClient)(nil).ImageBuild), arg0, arg1, arg2)
}

// ImageCreate mocks base method.
func (m *MockCommonAPIClient) ImageCreate(arg0 context.Context, arg1 string, arg2 image.CreateOptions) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImageCreate", arg0, arg1, arg2)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImageCreate indicates an expected call of ImageCreate.
func (mr *MockCommonAPIClientMockRecorder) ImageCreate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImageCreate", reflect.TypeOf((*MockCommonAPIClient)(nil).ImageCreate), arg0, arg1, arg2)
}

// ImageHistory mocks base method.
func (m *MockCommonAPIClient) ImageHistory(arg0 context.Context, arg1 string, arg2 ...client.ImageHistoryOption) ([]image.HistoryResponseItem, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ImageHistory", varargs...)
	ret0, _ := ret[0].([]image.HistoryResponseItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImageHistory indicates an expected call of ImageHistory.
func (mr *MockCommonAPIClientMockRecorder) ImageHistory(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImageHistory", reflect.TypeOf((*MockCommonAPIClient)(nil).ImageHistory), varargs...)
}

// ImageImport mocks base method.
func (m *MockCommonAPIClient) ImageImport(arg0 context.Context, arg1 image.ImportSource, arg2 string, arg3 image.ImportOptions) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImageImport", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImageImport indicates an expected call of ImageImport.
func (mr *MockCommonAPIClientMockRecorder) ImageImport(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImageImport", reflect.TypeOf((*MockCommonAPIClient)(nil).ImageImport), arg0, arg1, arg2, arg3)
}

// ImageInspect mocks base method.
func (m *MockCommonAPIClient) ImageInspect(arg0 context.Context, arg1 string, arg2 ...client.ImageInspectOption) (image.InspectResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ImageInspect", varargs...)
	ret0, _ := ret[0].(image.InspectResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImageInspect indicates an expected call of ImageInspect.
func (mr *MockCommonAPIClientMockRecorder) ImageInspect(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImageInspect", reflect.TypeOf((*MockCommonAPIClient)(nil).ImageInspect), varargs...)
}

// ImageInspectWithRaw mocks base method.
func (m *MockCommonAPIClient) ImageInspectWithRaw(arg0 context.Context, arg1 string) (image.InspectResponse, []byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImageInspectWithRaw", arg0, arg1)
	ret0, _ := ret[0].(image.InspectResponse)
	ret1, _ := ret[1].([]byte)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ImageInspectWithRaw indicates an expected call of ImageInspectWithRaw.
func (mr *MockCommonAPIClientMockRecorder) ImageInspectWithRaw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImageInspectWithRaw", reflect.TypeOf((*MockCommonAPIClient)(nil).ImageInspectWithRaw), arg0, arg1)
}

// ImageList mocks base method.
func (m *MockCommonAPIClient) ImageList(arg0 context.Context, arg1 image.ListOptions) ([]image.Summary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImageList", arg0, arg1)
	ret0, _ := ret[0].([]image.Summary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImageList indicates an expected call of ImageList.
func (mr *MockCommonAPIClientMockRecorder) ImageList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImageList", reflect.TypeOf((*MockCommonAPIClient)(nil).ImageList), arg0, arg1)
}

// ImageLoad mocks base method.
func (m *MockCommonAPIClient) ImageLoad(arg0 context.Context, arg1 io.Reader, arg2 ...client.ImageLoadOption) (image.LoadResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ImageLoad", varargs...)
	ret0, _ := ret[0].(image.LoadResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImageLoad indicates an expected call of ImageLoad.
func (mr *MockCommonAPIClientMockRecorder) ImageLoad(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImageLoad", reflect.TypeOf((*MockCommonAPIClient)(nil).ImageLoad), varargs...)
}

// ImagePull mocks base method.
func (m *MockCommonAPIClient) ImagePull(arg0 context.Context, arg1 string, arg2 image.PullOptions) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImagePull", arg0, arg1, arg2)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImagePull indicates an expected call of ImagePull.
func (mr *MockCommonAPIClientMockRecorder) ImagePull(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImagePull", reflect.TypeOf((*MockCommonAPIClient)(nil).ImagePull), arg0, arg1, arg2)
}

// ImagePush mocks base method.
func (m *MockCommonAPIClient) ImagePush(arg0 context.Context, arg1 string, arg2 image.PushOptions) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImagePush", arg0, arg1, arg2)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImagePush indicates an expected call of ImagePush.
func (mr *MockCommonAPIClientMockRecorder) ImagePush(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImagePush", reflect.TypeOf((*MockCommonAPIClient)(nil).ImagePush), arg0, arg1, arg2)
}

// ImageRemove mocks base method.
func (m *MockCommonAPIClient) ImageRemove(arg0 context.Context, arg1 string, arg2 image.RemoveOptions) ([]image.DeleteResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImageRemove", arg0, arg1, arg2)
	ret0, _ := ret[0].([]image.DeleteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImageRemove indicates an expected call of ImageRemove.
func (mr *MockCommonAPIClientMockRecorder) ImageRemove(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImageRemove", reflect.TypeOf((*MockCommonAPIClient)(nil).ImageRemove), arg0, arg1, arg2)
}

// ImageSave mocks base method.
func (m *MockCommonAPIClient) ImageSave(arg0 context.Context, arg1 []string, arg2 ...client.ImageSaveOption) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ImageSave", varargs...)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImageSave indicates an expected call of ImageSave.
func (mr *MockCommonAPIClientMockRecorder) ImageSave(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImageSave", reflect.TypeOf((*MockCommonAPIClient)(nil).ImageSave), varargs...)
}

// ImageSearch mocks base method.
func (m *MockCommonAPIClient) ImageSearch(arg0 context.Context, arg1 string, arg2 registry.SearchOptions) ([]registry.SearchResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImageSearch", arg0, arg1, arg2)
	ret0, _ := ret[0].([]registry.SearchResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImageSearch indicates an expected call of ImageSearch.
func (mr *MockCommonAPIClientMockRecorder) ImageSearch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImageSearch", reflect.TypeOf((*MockCommonAPIClient)(nil).ImageSearch), arg0, arg1, arg2)
}

// ImageTag mocks base method.
func (m *MockCommonAPIClient) ImageTag(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImageTag", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ImageTag indicates an expected call of ImageTag.
func (mr *MockCommonAPIClientMockRecorder) ImageTag(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImageTag", reflect.TypeOf((*MockCommonAPIClient)(nil).ImageTag), arg0, arg1, arg2)
}

// ImagesPrune mocks base method.
func (m *MockCommonAPIClient) ImagesPrune(arg0 context.Context, arg1 filters.Args) (image.PruneReport, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImagesPrune", arg0, arg1)
	ret0, _ := ret[0].(image.PruneReport)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImagesPrune indicates an expected call of ImagesPrune.
func (mr *MockCommonAPIClientMockRecorder) ImagesPrune(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImagesPrune", reflect.TypeOf((*MockCommonAPIClient)(nil).ImagesPrune), arg0, arg1)
}

// Info mocks base method.
func (m *MockCommonAPIClient) Info(arg0 context.Context) (system.Info, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Info", arg0)
	ret0, _ := ret[0].(system.Info)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Info indicates an expected call of Info.
func (mr *MockCommonAPIClientMockRecorder) Info(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Info", reflect.TypeOf((*MockCommonAPIClient)(nil).Info), arg0)
}

// NegotiateAPIVersion mocks base method.
func (m *MockCommonAPIClient) NegotiateAPIVersion(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "NegotiateAPIVersion", arg0)
}

// NegotiateAPIVersion indicates an expected call of NegotiateAPIVersion.
func (mr *MockCommonAPIClientMockRecorder) NegotiateAPIVersion(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NegotiateAPIVersion", reflect.TypeOf((*MockCommonAPIClient)(nil).NegotiateAPIVersion), arg0)
}

// NegotiateAPIVersionPing mocks base method.
func (m *MockCommonAPIClient) NegotiateAPIVersionPing(arg0 types.Ping) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "NegotiateAPIVersionPing", arg0)
}

// NegotiateAPIVersionPing indicates an expected call of NegotiateAPIVersionPing.
func (mr *MockCommonAPIClientMockRecorder) NegotiateAPIVersionPing(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NegotiateAPIVersionPing", reflect.TypeOf((*MockCommonAPIClient)(nil).NegotiateAPIVersionPing), arg0)
}

// NetworkConnect mocks base method.
func (m *MockCommonAPIClient) NetworkConnect(arg0 context.Context, arg1, arg2 string, arg3 *network.EndpointSettings) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NetworkConnect", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// NetworkConnect indicates an expected call of NetworkConnect.
func (mr *MockCommonAPIClientMockRecorder) NetworkConnect(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NetworkConnect", reflect.TypeOf((*MockCommonAPIClient)(nil).NetworkConnect), arg0, arg1, arg2, arg3)
}

// NetworkCreate mocks base method.
func (m *MockCommonAPIClient) NetworkCreate(arg0 context.Context, arg1 string, arg2 network.CreateOptions) (network.CreateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NetworkCreate", arg0, arg1, arg2)
	ret0, _ := ret[0].(network.CreateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NetworkCreate indicates an expected call of NetworkCreate.
func (mr *MockCommonAPIClientMockRecorder) NetworkCreate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NetworkCreate", reflect.TypeOf((*MockCommonAPIClient)(nil).NetworkCreate), arg0, arg1, arg2)
}

// NetworkDisconnect mocks base method.
func (m *MockCommonAPIClient) NetworkDisconnect(arg0 context.Context, arg1, arg2 string, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NetworkDisconnect", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// NetworkDisconnect indicates an expected call of NetworkDisconnect.
func (mr *MockCommonAPIClientMockRecorder) NetworkDisconnect(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NetworkDisconnect", reflect.TypeOf((*MockCommonAPIClient)(nil).NetworkDisconnect), arg0, arg1, arg2, arg3)
}

// NetworkInspect mocks base method.
func (m *MockCommonAPIClient) NetworkInspect(arg0 context.Context, arg1 string, arg2 network.InspectOptions) (network.Inspect, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NetworkInspect", arg0, arg1, arg2)
	ret0, _ := ret[0].(network.Inspect)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NetworkInspect indicates an expected call of NetworkInspect.
func (mr *MockCommonAPIClientMockRecorder) NetworkInspect(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NetworkInspect", reflect.TypeOf((*MockCommonAPIClient)(nil).NetworkInspect), arg0, arg1, arg2)
}

// NetworkInspectWithRaw mocks base method.
func (m *MockCommonAPIClient) NetworkInspectWithRaw(arg0 context.Context, arg1 string, arg2 network.InspectOptions) (network.Inspect, []byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NetworkInspectWithRaw", arg0, arg1, arg2)
	ret0, _ := ret[0].(network.Inspect)
	ret1, _ := ret[1].([]byte)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// NetworkInspectWithRaw indicates an expected call of NetworkInspectWithRaw.
func (mr *MockCommonAPIClientMockRecorder) NetworkInspectWithRaw(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NetworkInspectWithRaw", reflect.TypeOf((*MockCommonAPIClient)(nil).NetworkInspectWithRaw), arg0, arg1, arg2)
}

// NetworkList mocks base method.
func (m *MockCommonAPIClient) NetworkList(arg0 context.Context, arg1 network.ListOptions) ([]network.Inspect, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NetworkList", arg0, arg1)
	ret0, _ := ret[0].([]network.Inspect)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NetworkList indicates an expected call of NetworkList.
func (mr *MockCommonAPIClientMockRecorder) NetworkList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NetworkList", reflect.TypeOf((*MockCommonAPIClient)(nil).NetworkList), arg0, arg1)
}

// NetworkRemove mocks base method.
func (m *MockCommonAPIClient) NetworkRemove(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NetworkRemove", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// NetworkRemove indicates an expected call of NetworkRemove.
func (mr *MockCommonAPIClientMockRecorder) NetworkRemove(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NetworkRemove", reflect.TypeOf((*MockCommonAPIClient)(nil).NetworkRemove), arg0, arg1)
}

// NetworksPrune mocks base method.
func (m *MockCommonAPIClient) NetworksPrune(arg0 context.Context, arg1 filters.Args) (network.PruneReport, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NetworksPrune", arg0, arg1)
	ret0, _ := ret[0].(network.PruneReport)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NetworksPrune indicates an expected call of NetworksPrune.
func (mr *MockCommonAPIClientMockRecorder) NetworksPrune(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NetworksPrune", reflect.TypeOf((*MockCommonAPIClient)(nil).NetworksPrune), arg0, arg1)
}

// NodeInspectWithRaw mocks base method.
func (m *MockCommonAPIClient) NodeInspectWithRaw(arg0 context.Context, arg1 string) (swarm.Node, []byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeInspectWithRaw", arg0, arg1)
	ret0, _ := ret[0].(swarm.Node)
	ret1, _ := ret[1].([]byte)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// NodeInspectWithRaw indicates an expected call of NodeInspectWithRaw.
func (mr *MockCommonAPIClientMockRecorder) NodeInspectWithRaw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeInspectWithRaw", reflect.TypeOf((*MockCommonAPIClient)(nil).NodeInspectWithRaw), arg0, arg1)
}

// NodeList mocks base method.
func (m *MockCommonAPIClient) NodeList(arg0 context.Context, arg1 types.NodeListOptions) ([]swarm.Node, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeList", arg0, arg1)
	ret0, _ := ret[0].([]swarm.Node)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NodeList indicates an expected call of NodeList.
func (mr *MockCommonAPIClientMockRecorder) NodeList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeList", reflect.TypeOf((*MockCommonAPIClient)(nil).NodeList), arg0, arg1)
}

// NodeRemove mocks base method.
func (m *MockCommonAPIClient) NodeRemove(arg0 context.Context, arg1 string, arg2 types.NodeRemoveOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeRemove", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// NodeRemove indicates an expected call of NodeRemove.
func (mr *MockCommonAPIClientMockRecorder) NodeRemove(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeRemove", reflect.TypeOf((*MockCommonAPIClient)(nil).NodeRemove), arg0, arg1, arg2)
}

// NodeUpdate mocks base method.
func (m *MockCommonAPIClient) NodeUpdate(arg0 context.Context, arg1 string, arg2 swarm.Version, arg3 swarm.NodeSpec) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeUpdate", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// NodeUpdate indicates an expected call of NodeUpdate.
func (mr *MockCommonAPIClientMockRecorder) NodeUpdate(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeUpdate", reflect.TypeOf((*MockCommonAPIClient)(nil).NodeUpdate), arg0, arg1, arg2, arg3)
}

// Ping mocks base method.
func (m *MockCommonAPIClient) Ping(arg0 context.Context) (types.Ping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Ping", arg0)
	ret0, _ := ret[0].(types.Ping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Ping indicates an expected call of Ping.
func (mr *MockCommonAPIClientMockRecorder) Ping(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ping", reflect.TypeOf((*MockCommonAPIClient)(nil).Ping), arg0)
}

// PluginCreate mocks base method.
func (m *MockCommonAPIClient) PluginCreate(arg0 context.Context, arg1 io.Reader, arg2 types.PluginCreateOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PluginCreate", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PluginCreate indicates an expected call of PluginCreate.
func (mr *MockCommonAPIClientMockRecorder) PluginCreate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PluginCreate", reflect.TypeOf((*MockCommonAPIClient)(nil).PluginCreate), arg0, arg1, arg2)
}

// PluginDisable mocks base method.
func (m *MockCommonAPIClient) PluginDisable(arg0 context.Context, arg1 string, arg2 types.PluginDisableOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PluginDisable", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PluginDisable indicates an expected call of PluginDisable.
func (mr *MockCommonAPIClientMockRecorder) PluginDisable(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PluginDisable", reflect.TypeOf((*MockCommonAPIClient)(nil).PluginDisable), arg0, arg1, arg2)
}

// PluginEnable mocks base method.
func (m *MockCommonAPIClient) PluginEnable(arg0 context.Context, arg1 string, arg2 types.PluginEnableOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PluginEnable", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PluginEnable indicates an expected call of PluginEnable.
func (mr *MockCommonAPIClientMockRecorder) PluginEnable(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PluginEnable", reflect.TypeOf((*MockCommonAPIClient)(nil).PluginEnable), arg0, arg1, arg2)
}

// PluginInspectWithRaw mocks base method.
func (m *MockCommonAPIClient) PluginInspectWithRaw(arg0 context.Context, arg1 string) (*types.Plugin, []byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PluginInspectWithRaw", arg0, arg1)
	ret0, _ := ret[0].(*types.Plugin)
	ret1, _ := ret[1].([]byte)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// PluginInspectWithRaw indicates an expected call of PluginInspectWithRaw.
func (mr *MockCommonAPIClientMockRecorder) PluginInspectWithRaw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PluginInspectWithRaw", reflect.TypeOf((*MockCommonAPIClient)(nil).PluginInspectWithRaw), arg0, arg1)
}

// PluginInstall mocks base method.
func (m *MockCommonAPIClient) PluginInstall(arg0 context.Context, arg1 string, arg2 types.PluginInstallOptions) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PluginInstall", arg0, arg1, arg2)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PluginInstall indicates an expected call of PluginInstall.
func (mr *MockCommonAPIClientMockRecorder) PluginInstall(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PluginInstall", reflect.TypeOf((*MockCommonAPIClient)(nil).PluginInstall), arg0, arg1, arg2)
}

// PluginList mocks base method.
func (m *MockCommonAPIClient) PluginList(arg0 context.Context, arg1 filters.Args) (types.PluginsListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PluginList", arg0, arg1)
	ret0, _ := ret[0].(types.PluginsListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PluginList indicates an expected call of PluginList.
func (mr *MockCommonAPIClientMockRecorder) PluginList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PluginList", reflect.TypeOf((*MockCommonAPIClient)(nil).PluginList), arg0, arg1)
}

// PluginPush mocks base method.
func (m *MockCommonAPIClient) PluginPush(arg0 context.Context, arg1, arg2 string) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PluginPush", arg0, arg1, arg2)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PluginPush indicates an expected call of PluginPush.
func (mr *MockCommonAPIClientMockRecorder) PluginPush(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PluginPush", reflect.TypeOf((*MockCommonAPIClient)(nil).PluginPush), arg0, arg1, arg2)
}

// PluginRemove mocks base method.
func (m *MockCommonAPIClient) PluginRemove(arg0 context.Context, arg1 string, arg2 types.PluginRemoveOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PluginRemove", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PluginRemove indicates an expected call of PluginRemove.
func (mr *MockCommonAPIClientMockRecorder) PluginRemove(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PluginRemove", reflect.TypeOf((*MockCommonAPIClient)(nil).PluginRemove), arg0, arg1, arg2)
}

// PluginSet mocks base method.
func (m *MockCommonAPIClient) PluginSet(arg0 context.Context, arg1 string, arg2 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PluginSet", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PluginSet indicates an expected call of PluginSet.
func (mr *MockCommonAPIClientMockRecorder) PluginSet(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PluginSet", reflect.TypeOf((*MockCommonAPIClient)(nil).PluginSet), arg0, arg1, arg2)
}

// PluginUpgrade mocks base method.
func (m *MockCommonAPIClient) PluginUpgrade(arg0 context.Context, arg1 string, arg2 types.PluginInstallOptions) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PluginUpgrade", arg0, arg1, arg2)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PluginUpgrade indicates an expected call of PluginUpgrade.
func (mr *MockCommonAPIClientMockRecorder) PluginUpgrade(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PluginUpgrade", reflect.TypeOf((*MockCommonAPIClient)(nil).PluginUpgrade), arg0, arg1, arg2)
}

// RegistryLogin mocks base method.
func (m *MockCommonAPIClient) RegistryLogin(arg0 context.Context, arg1 registry.AuthConfig) (registry.AuthenticateOKBody, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RegistryLogin", arg0, arg1)
	ret0, _ := ret[0].(registry.AuthenticateOKBody)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RegistryLogin indicates an expected call of RegistryLogin.
func (mr *MockCommonAPIClientMockRecorder) RegistryLogin(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegistryLogin", reflect.TypeOf((*MockCommonAPIClient)(nil).RegistryLogin), arg0, arg1)
}

// SecretCreate mocks base method.
func (m *MockCommonAPIClient) SecretCreate(arg0 context.Context, arg1 swarm.SecretSpec) (types.SecretCreateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SecretCreate", arg0, arg1)
	ret0, _ := ret[0].(types.SecretCreateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SecretCreate indicates an expected call of SecretCreate.
func (mr *MockCommonAPIClientMockRecorder) SecretCreate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SecretCreate", reflect.TypeOf((*MockCommonAPIClient)(nil).SecretCreate), arg0, arg1)
}

// SecretInspectWithRaw mocks base method.
func (m *MockCommonAPIClient) SecretInspectWithRaw(arg0 context.Context, arg1 string) (swarm.Secret, []byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SecretInspectWithRaw", arg0, arg1)
	ret0, _ := ret[0].(swarm.Secret)
	ret1, _ := ret[1].([]byte)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// SecretInspectWithRaw indicates an expected call of SecretInspectWithRaw.
func (mr *MockCommonAPIClientMockRecorder) SecretInspectWithRaw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SecretInspectWithRaw", reflect.TypeOf((*MockCommonAPIClient)(nil).SecretInspectWithRaw), arg0, arg1)
}

// SecretList mocks base method.
func (m *MockCommonAPIClient) SecretList(arg0 context.Context, arg1 types.SecretListOptions) ([]swarm.Secret, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SecretList", arg0, arg1)
	ret0, _ := ret[0].([]swarm.Secret)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SecretList indicates an expected call of SecretList.
func (mr *MockCommonAPIClientMockRecorder) SecretList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SecretList", reflect.TypeOf((*MockCommonAPIClient)(nil).SecretList), arg0, arg1)
}

// SecretRemove mocks base method.
func (m *MockCommonAPIClient) SecretRemove(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SecretRemove", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SecretRemove indicates an expected call of SecretRemove.
func (mr *MockCommonAPIClientMockRecorder) SecretRemove(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SecretRemove", reflect.TypeOf((*MockCommonAPIClient)(nil).SecretRemove), arg0, arg1)
}

// SecretUpdate mocks base method.
func (m *MockCommonAPIClient) SecretUpdate(arg0 context.Context, arg1 string, arg2 swarm.Version, arg3 swarm.SecretSpec) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SecretUpdate", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SecretUpdate indicates an expected call of SecretUpdate.
func (mr *MockCommonAPIClientMockRecorder) SecretUpdate(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SecretUpdate", reflect.TypeOf((*MockCommonAPIClient)(nil).SecretUpdate), arg0, arg1, arg2, arg3)
}

// ServerVersion mocks base method.
func (m *MockCommonAPIClient) ServerVersion(arg0 context.Context) (types.Version, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ServerVersion", arg0)
	ret0, _ := ret[0].(types.Version)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ServerVersion indicates an expected call of ServerVersion.
func (mr *MockCommonAPIClientMockRecorder) ServerVersion(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ServerVersion", reflect.TypeOf((*MockCommonAPIClient)(nil).ServerVersion), arg0)
}

// ServiceCreate mocks base method.
func (m *MockCommonAPIClient) ServiceCreate(arg0 context.Context, arg1 swarm.ServiceSpec, arg2 types.ServiceCreateOptions) (swarm.ServiceCreateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ServiceCreate", arg0, arg1, arg2)
	ret0, _ := ret[0].(swarm.ServiceCreateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ServiceCreate indicates an expected call of ServiceCreate.
func (mr *MockCommonAPIClientMockRecorder) ServiceCreate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ServiceCreate", reflect.TypeOf((*MockCommonAPIClient)(nil).ServiceCreate), arg0, arg1, arg2)
}

// ServiceInspectWithRaw mocks base method.
func (m *MockCommonAPIClient) ServiceInspectWithRaw(arg0 context.Context, arg1 string, arg2 types.ServiceInspectOptions) (swarm.Service, []byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ServiceInspectWithRaw", arg0, arg1, arg2)
	ret0, _ := ret[0].(swarm.Service)
	ret1, _ := ret[1].([]byte)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ServiceInspectWithRaw indicates an expected call of ServiceInspectWithRaw.
func (mr *MockCommonAPIClientMockRecorder) ServiceInspectWithRaw(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ServiceInspectWithRaw", reflect.TypeOf((*MockCommonAPIClient)(nil).ServiceInspectWithRaw), arg0, arg1, arg2)
}

// ServiceList mocks base method.
func (m *MockCommonAPIClient) ServiceList(arg0 context.Context, arg1 types.ServiceListOptions) ([]swarm.Service, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ServiceList", arg0, arg1)
	ret0, _ := ret[0].([]swarm.Service)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ServiceList indicates an expected call of ServiceList.
func (mr *MockCommonAPIClientMockRecorder) ServiceList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ServiceList", reflect.TypeOf((*MockCommonAPIClient)(nil).ServiceList), arg0, arg1)
}

// ServiceLogs mocks base method.
func (m *MockCommonAPIClient) ServiceLogs(arg0 context.Context, arg1 string, arg2 container.LogsOptions) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ServiceLogs", arg0, arg1, arg2)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ServiceLogs indicates an expected call of ServiceLogs.
func (mr *MockCommonAPIClientMockRecorder) ServiceLogs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ServiceLogs", reflect.TypeOf((*MockCommonAPIClient)(nil).ServiceLogs), arg0, arg1, arg2)
}

// ServiceRemove mocks base method.
func (m *MockCommonAPIClient) ServiceRemove(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ServiceRemove", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ServiceRemove indicates an expected call of ServiceRemove.
func (mr *MockCommonAPIClientMockRecorder) ServiceRemove(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ServiceRemove", reflect.TypeOf((*MockCommonAPIClient)(nil).ServiceRemove), arg0, arg1)
}

// ServiceUpdate mocks base method.
func (m *MockCommonAPIClient) ServiceUpdate(arg0 context.Context, arg1 string, arg2 swarm.Version, arg3 swarm.ServiceSpec, arg4 types.ServiceUpdateOptions) (swarm.ServiceUpdateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ServiceUpdate", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(swarm.ServiceUpdateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ServiceUpdate indicates an expected call of ServiceUpdate.
func (mr *MockCommonAPIClientMockRecorder) ServiceUpdate(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ServiceUpdate", reflect.TypeOf((*MockCommonAPIClient)(nil).ServiceUpdate), arg0, arg1, arg2, arg3, arg4)
}

// SwarmGetUnlockKey mocks base method.
func (m *MockCommonAPIClient) SwarmGetUnlockKey(arg0 context.Context) (types.SwarmUnlockKeyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwarmGetUnlockKey", arg0)
	ret0, _ := ret[0].(types.SwarmUnlockKeyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SwarmGetUnlockKey indicates an expected call of SwarmGetUnlockKey.
func (mr *MockCommonAPIClientMockRecorder) SwarmGetUnlockKey(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwarmGetUnlockKey", reflect.TypeOf((*MockCommonAPIClient)(nil).SwarmGetUnlockKey), arg0)
}

// SwarmInit mocks base method.
func (m *MockCommonAPIClient) SwarmInit(arg0 context.Context, arg1 swarm.InitRequest) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwarmInit", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SwarmInit indicates an expected call of SwarmInit.
func (mr *MockCommonAPIClientMockRecorder) SwarmInit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwarmInit", reflect.TypeOf((*MockCommonAPIClient)(nil).SwarmInit), arg0, arg1)
}

// SwarmInspect mocks base method.
func (m *MockCommonAPIClient) SwarmInspect(arg0 context.Context) (swarm.Swarm, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwarmInspect", arg0)
	ret0, _ := ret[0].(swarm.Swarm)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SwarmInspect indicates an expected call of SwarmInspect.
func (mr *MockCommonAPIClientMockRecorder) SwarmInspect(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwarmInspect", reflect.TypeOf((*MockCommonAPIClient)(nil).SwarmInspect), arg0)
}

// SwarmJoin mocks base method.
func (m *MockCommonAPIClient) SwarmJoin(arg0 context.Context, arg1 swarm.JoinRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwarmJoin", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SwarmJoin indicates an expected call of SwarmJoin.
func (mr *MockCommonAPIClientMockRecorder) SwarmJoin(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwarmJoin", reflect.TypeOf((*MockCommonAPIClient)(nil).SwarmJoin), arg0, arg1)
}

// SwarmLeave mocks base method.
func (m *MockCommonAPIClient) SwarmLeave(arg0 context.Context, arg1 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwarmLeave", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SwarmLeave indicates an expected call of SwarmLeave.
func (mr *MockCommonAPIClientMockRecorder) SwarmLeave(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwarmLeave", reflect.TypeOf((*MockCommonAPIClient)(nil).SwarmLeave), arg0, arg1)
}

// SwarmUnlock mocks base method.
func (m *MockCommonAPIClient) SwarmUnlock(arg0 context.Context, arg1 swarm.UnlockRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwarmUnlock", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SwarmUnlock indicates an expected call of SwarmUnlock.
func (mr *MockCommonAPIClientMockRecorder) SwarmUnlock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwarmUnlock", reflect.TypeOf((*MockCommonAPIClient)(nil).SwarmUnlock), arg0, arg1)
}

// SwarmUpdate mocks base method.
func (m *MockCommonAPIClient) SwarmUpdate(arg0 context.Context, arg1 swarm.Version, arg2 swarm.Spec, arg3 swarm.UpdateFlags) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwarmUpdate", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SwarmUpdate indicates an expected call of SwarmUpdate.
func (mr *MockCommonAPIClientMockRecorder) SwarmUpdate(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwarmUpdate", reflect.TypeOf((*MockCommonAPIClient)(nil).SwarmUpdate), arg0, arg1, arg2, arg3)
}

// TaskInspectWithRaw mocks base method.
func (m *MockCommonAPIClient) TaskInspectWithRaw(arg0 context.Context, arg1 string) (swarm.Task, []byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TaskInspectWithRaw", arg0, arg1)
	ret0, _ := ret[0].(swarm.Task)
	ret1, _ := ret[1].([]byte)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// TaskInspectWithRaw indicates an expected call of TaskInspectWithRaw.
func (mr *MockCommonAPIClientMockRecorder) TaskInspectWithRaw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TaskInspectWithRaw", reflect.TypeOf((*MockCommonAPIClient)(nil).TaskInspectWithRaw), arg0, arg1)
}

// TaskList mocks base method.
func (m *MockCommonAPIClient) TaskList(arg0 context.Context, arg1 types.TaskListOptions) ([]swarm.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TaskList", arg0, arg1)
	ret0, _ := ret[0].([]swarm.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TaskList indicates an expected call of TaskList.
func (mr *MockCommonAPIClientMockRecorder) TaskList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TaskList", reflect.TypeOf((*MockCommonAPIClient)(nil).TaskList), arg0, arg1)
}

// TaskLogs mocks base method.
func (m *MockCommonAPIClient) TaskLogs(arg0 context.Context, arg1 string, arg2 container.LogsOptions) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TaskLogs", arg0, arg1, arg2)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TaskLogs indicates an expected call of TaskLogs.
func (mr *MockCommonAPIClientMockRecorder) TaskLogs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TaskLogs", reflect.TypeOf((*MockCommonAPIClient)(nil).TaskLogs), arg0, arg1, arg2)
}

// VolumeCreate mocks base method.
func (m *MockCommonAPIClient) VolumeCreate(arg0 context.Context, arg1 volume.CreateOptions) (volume.Volume, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VolumeCreate", arg0, arg1)
	ret0, _ := ret[0].(volume.Volume)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VolumeCreate indicates an expected call of VolumeCreate.
func (mr *MockCommonAPIClientMockRecorder) VolumeCreate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VolumeCreate", reflect.TypeOf((*MockCommonAPIClient)(nil).VolumeCreate), arg0, arg1)
}

// VolumeInspect mocks base method.
func (m *MockCommonAPIClient) VolumeInspect(arg0 context.Context, arg1 string) (volume.Volume, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VolumeInspect", arg0, arg1)
	ret0, _ := ret[0].(volume.Volume)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VolumeInspect indicates an expected call of VolumeInspect.
func (mr *MockCommonAPIClientMockRecorder) VolumeInspect(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VolumeInspect", reflect.TypeOf((*MockCommonAPIClient)(nil).VolumeInspect), arg0, arg1)
}

// VolumeInspectWithRaw mocks base method.
func (m *MockCommonAPIClient) VolumeInspectWithRaw(arg0 context.Context, arg1 string) (volume.Volume, []byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VolumeInspectWithRaw", arg0, arg1)
	ret0, _ := ret[0].(volume.Volume)
	ret1, _ := ret[1].([]byte)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// VolumeInspectWithRaw indicates an expected call of VolumeInspectWithRaw.
func (mr *MockCommonAPIClientMockRecorder) VolumeInspectWithRaw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VolumeInspectWithRaw", reflect.TypeOf((*MockCommonAPIClient)(nil).VolumeInspectWithRaw), arg0, arg1)
}

// VolumeList mocks base method.
func (m *MockCommonAPIClient) VolumeList(arg0 context.Context, arg1 volume.ListOptions) (volume.ListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VolumeList", arg0, arg1)
	ret0, _ := ret[0].(volume.ListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VolumeList indicates an expected call of VolumeList.
func (mr *MockCommonAPIClientMockRecorder) VolumeList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VolumeList", reflect.TypeOf((*MockCommonAPIClient)(nil).VolumeList), arg0, arg1)
}

// VolumeRemove mocks base method.
func (m *MockCommonAPIClient) VolumeRemove(arg0 context.Context, arg1 string, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VolumeRemove", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// VolumeRemove indicates an expected call of VolumeRemove.
func (mr *MockCommonAPIClientMockRecorder) VolumeRemove(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VolumeRemove", reflect.TypeOf((*MockCommonAPIClient)(nil).VolumeRemove), arg0, arg1, arg2)
}

// VolumeUpdate mocks base method.
func (m *MockCommonAPIClient) VolumeUpdate(arg0 context.Context, arg1 string, arg2 swarm.Version, arg3 volume.UpdateOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VolumeUpdate", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// VolumeUpdate indicates an expected call of VolumeUpdate.
func (mr *MockCommonAPIClientMockRecorder) VolumeUpdate(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VolumeUpdate", reflect.TypeOf((*MockCommonAPIClient)(nil).VolumeUpdate), arg0, arg1, arg2, arg3)
}

// VolumesPrune mocks base method.
func (m *MockCommonAPIClient) VolumesPrune(arg0 context.Context, arg1 filters.Args) (volume.PruneReport, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VolumesPrune", arg0, arg1)
	ret0, _ := ret[0].(volume.PruneReport)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VolumesPrune indicates an expected call of VolumesPrune.
func (mr *MockCommonAPIClientMockRecorder) VolumesPrune(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VolumesPrune", reflect.TypeOf((*MockCommonAPIClient)(nil).VolumesPrune), arg0, arg1)
}
