// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/buildpacks/pack/pkg/client (interfaces: AccessChecker)

// Package testmocks is a generated GoMock package.
package testmocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockAccessChecker is a mock of AccessChecker interface.
type MockAccessChecker struct {
	ctrl     *gomock.Controller
	recorder *MockAccessCheckerMockRecorder
}

// MockAccessCheckerMockRecorder is the mock recorder for MockAccessChecker.
type MockAccessCheckerMockRecorder struct {
	mock *MockAccessChecker
}

// NewMockAccessChecker creates a new mock instance.
func NewMockAccessChecker(ctrl *gomock.Controller) *MockAccessChecker {
	mock := &MockAccessChecker{ctrl: ctrl}
	mock.recorder = &MockAccessCheckerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccessChecker) EXPECT() *MockAccessCheckerMockRecorder {
	return m.recorder
}

// Check mocks base method.
func (m *MockAccessChecker) Check(arg0 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Check", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Check indicates an expected call of Check.
func (mr *MockAccessCheckerMockRecorder) Check(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Check", reflect.TypeOf((*MockAccessChecker)(nil).Check), arg0)
}
