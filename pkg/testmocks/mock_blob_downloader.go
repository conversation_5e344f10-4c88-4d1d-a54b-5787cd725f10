// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/buildpacks/pack/pkg/client (interfaces: BlobDownloader)

// Package testmocks is a generated GoMock package.
package testmocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	blob "github.com/buildpacks/pack/pkg/blob"
)

// MockBlobDownloader is a mock of BlobDownloader interface.
type MockBlobDownloader struct {
	ctrl     *gomock.Controller
	recorder *MockBlobDownloaderMockRecorder
}

// MockBlobDownloaderMockRecorder is the mock recorder for MockBlobDownloader.
type MockBlobDownloaderMockRecorder struct {
	mock *MockBlobDownloader
}

// NewMockBlobDownloader creates a new mock instance.
func NewMockBlobDownloader(ctrl *gomock.Controller) *MockBlobDownloader {
	mock := &MockBlobDownloader{ctrl: ctrl}
	mock.recorder = &MockBlobDownloaderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBlobDownloader) EXPECT() *MockBlobDownloaderMockRecorder {
	return m.recorder
}

// Download mocks base method.
func (m *MockBlobDownloader) Download(arg0 context.Context, arg1 string) (blob.Blob, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Download", arg0, arg1)
	ret0, _ := ret[0].(blob.Blob)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Download indicates an expected call of Download.
func (mr *MockBlobDownloaderMockRecorder) Download(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Download", reflect.TypeOf((*MockBlobDownloader)(nil).Download), arg0, arg1)
}
